import axios from 'axios';

// API基础配置
const API_BASE_URL = 'http://localhost:3002/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log(`🌐 API请求: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API响应: ${response.config.url} - ${response.status}`);
    return response;
  },
  (error) => {
    console.error('❌ API响应错误:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API服务类
class ApiService {
  // 获取对话列表
  async getConversations(params = {}) {
    try {
      const response = await api.get('/conversations', { params });
      return response.data;
    } catch (error) {
      throw new Error(`获取对话列表失败: ${error.message}`);
    }
  }

  // 获取对话中的消息
  async getConversationMessages(conversationId, params = {}) {
    try {
      const response = await api.get(`/conversations/${conversationId}/messages`, { params });
      return response.data;
    } catch (error) {
      throw new Error(`获取对话消息失败: ${error.message}`);
    }
  }

  // 获取用户列表
  async getUsers(params = {}) {
    try {
      const response = await api.get('/users', { params });
      return response.data;
    } catch (error) {
      throw new Error(`获取用户列表失败: ${error.message}`);
    }
  }

  // 搜索消息
  async searchMessages(query, params = {}) {
    try {
      const response = await api.get('/search', { 
        params: { q: query, ...params } 
      });
      return response.data;
    } catch (error) {
      throw new Error(`搜索消息失败: ${error.message}`);
    }
  }

  // 获取统计信息
  async getStatistics(params = {}) {
    try {
      const response = await api.get('/stats', { params });
      return response.data;
    } catch (error) {
      throw new Error(`获取统计信息失败: ${error.message}`);
    }
  }

  // 获取webhook日志
  async getWebhookLogs(params = {}) {
    try {
      const response = await api.get('/webhook-logs', { params });
      return response.data;
    } catch (error) {
      throw new Error(`获取webhook日志失败: ${error.message}`);
    }
  }

  // 测试API连接
  async testConnection() {
    try {
      const response = await api.get('/');
      return response.data;
    } catch (error) {
      throw new Error(`API连接测试失败: ${error.message}`);
    }
  }
}

// 导出单例实例
export const apiService = new ApiService();
export default apiService;
