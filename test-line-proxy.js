// LINE代理配置测试脚本
require('dotenv').config();

const axios = require('axios');
const line = require('@line/bot-sdk');
const { HttpsProxyAgent } = require('https-proxy-agent');

console.log('🧪 LINE代理配置测试');
console.log('=' .repeat(50));

// 配置全局代理（用于LINE SDK和其他HTTPS请求）
const proxyUrl = process.env.HTTPS_PROXY || process.env.HTTP_PROXY || 'http://127.0.0.1:7890';
if (proxyUrl && (process.env.USE_PROXY === 'true' || process.env.NODE_ENV === 'development')) {
  console.log('🌐 配置全局HTTPS代理:', proxyUrl);

  // 创建代理agent
  const httpsAgent = new HttpsProxyAgent(proxyUrl);

  // 配置axios全局默认设置
  axios.defaults.httpsAgent = httpsAgent;
  axios.defaults.proxy = false; // 禁用axios内置代理，使用httpsAgent

  // 配置Node.js全局fetch代理（LINE SDK v10使用fetch）
  const { setGlobalDispatcher, ProxyAgent } = require('undici');
  try {
    const proxyAgent = new ProxyAgent(proxyUrl);
    setGlobalDispatcher(proxyAgent);
    console.log('✅ 全局fetch代理配置完成（undici）');
  } catch (undiciError) {
    console.log('⚠️ undici代理配置失败:', undiciError.message);
  }

  console.log('✅ 代理配置完成，LINE SDK和axios将使用此代理');
} else {
  console.log('⚠️ 代理未启用或未配置');
}

// 测试函数
async function testLineProxy() {
  try {
    console.log('\n📱 开始测试LINE API连接...');
    
    // 检查环境变量
    if (!process.env.LINE_CHANNEL_ACCESS_TOKEN) {
      throw new Error('LINE_CHANNEL_ACCESS_TOKEN未配置');
    }
    
    // 初始化LINE客户端
    const lineClient = new line.messagingApi.MessagingApiClient({
      channelAccessToken: process.env.LINE_CHANNEL_ACCESS_TOKEN
    });
    
    console.log('✅ LINE客户端初始化成功');
    
    // 测试获取Bot信息
    console.log('🔍 测试获取Bot信息...');
    const botInfo = await lineClient.getBotInfo();
    
    console.log('✅ LINE API连接成功!');
    console.log('📋 Bot信息:');
    console.log('   Bot ID:', botInfo.userId);
    console.log('   显示名称:', botInfo.displayName);
    console.log('   头像URL:', botInfo.pictureUrl || 'N/A');
    
    return true;
    
  } catch (error) {
    console.error('❌ LINE API连接失败:', error.message);
    
    // 详细的错误分析
    if (error.response) {
      console.error('   HTTP状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
    
    if (error.code) {
      console.error('   错误代码:', error.code);
    }
    
    // 针对不同错误类型提供建议
    if (error.message.includes('fetch failed') || error.message.includes('ECONNREFUSED')) {
      console.error('   💡 建议: 网络连接失败，请检查:');
      console.error('      1. 代理服务器是否正常运行 (127.0.0.1:7890)');
      console.error('      2. 代理配置是否正确');
      console.error('      3. 网络连接是否正常');
    } else if (error.message.includes('Invalid channel access token')) {
      console.error('   💡 建议: Channel access token无效，请检查环境变量配置');
    } else if (error.message.includes('timeout')) {
      console.error('   💡 建议: 请求超时，可能是代理配置问题');
    }
    
    return false;
  }
}

// 测试直接axios请求（用于对比）
async function testDirectAxiosRequest() {
  try {
    console.log('\n🌐 测试直接axios HTTPS请求...');
    
    const response = await axios.get('https://api.line.me/v2/bot/info', {
      headers: {
        'Authorization': `Bearer ${process.env.LINE_CHANNEL_ACCESS_TOKEN}`
      },
      timeout: 10000
    });
    
    console.log('✅ 直接axios请求成功');
    console.log('   状态码:', response.status);
    console.log('   响应数据:', JSON.stringify(response.data, null, 2));
    
    return true;
    
  } catch (error) {
    console.error('❌ 直接axios请求失败:', error.message);
    
    if (error.response) {
      console.error('   HTTP状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
    
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('\n🚀 开始运行测试...');
  console.log('📋 测试环境:');
  console.log('   USE_PROXY:', process.env.USE_PROXY);
  console.log('   HTTPS_PROXY:', process.env.HTTPS_PROXY);
  console.log('   NODE_ENV:', process.env.NODE_ENV);
  console.log('   LINE_CHANNEL_ACCESS_TOKEN:', process.env.LINE_CHANNEL_ACCESS_TOKEN ? '已配置' : '未配置');
  
  // 测试1: LINE SDK API调用
  const lineTest = await testLineProxy();
  
  // 测试2: 直接axios请求
  const axiosTest = await testDirectAxiosRequest();
  
  // 总结
  console.log('\n📊 测试结果总结:');
  console.log('   LINE SDK测试:', lineTest ? '✅ 成功' : '❌ 失败');
  console.log('   直接axios测试:', axiosTest ? '✅ 成功' : '❌ 失败');
  
  if (lineTest && axiosTest) {
    console.log('\n🎉 所有测试通过！LINE代理配置正常工作');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查配置');
  }
}

// 运行测试
runTests().catch(error => {
  console.error('❌ 测试运行失败:', error.message);
  process.exit(1);
});
