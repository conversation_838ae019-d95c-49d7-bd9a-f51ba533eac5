// API测试脚本
const axios = require('axios');

const API_BASE_URL = 'http://localhost:3002/api';

async function testAPI() {
  console.log('🧪 开始测试API端点...\n');

  try {
    // 测试API根路径
    console.log('1. 测试API根路径...');
    const rootResponse = await axios.get(`${API_BASE_URL}/`);
    console.log('✅ API根路径响应:', rootResponse.data.message);

    // 测试统计信息
    console.log('\n2. 测试统计信息端点...');
    const statsResponse = await axios.get(`${API_BASE_URL}/stats`);
    console.log('✅ 统计信息:', {
      totalUsers: statsResponse.data.data.general.totalUsers,
      totalConversations: statsResponse.data.data.general.totalConversations,
      totalMessages: statsResponse.data.data.general.totalMessages,
      platforms: statsResponse.data.data.platforms.length
    });

    // 测试对话列表
    console.log('\n3. 测试对话列表端点...');
    const conversationsResponse = await axios.get(`${API_BASE_URL}/conversations`);
    console.log('✅ 对话列表:', {
      count: conversationsResponse.data.data.length,
      pagination: conversationsResponse.data.pagination
    });

    // 测试用户列表
    console.log('\n4. 测试用户列表端点...');
    const usersResponse = await axios.get(`${API_BASE_URL}/users`);
    console.log('✅ 用户列表:', {
      count: usersResponse.data.data.length,
      pagination: usersResponse.data.pagination
    });

    // 测试搜索功能
    console.log('\n5. 测试搜索端点...');
    const searchResponse = await axios.get(`${API_BASE_URL}/search?q=test`);
    console.log('✅ 搜索结果:', {
      query: searchResponse.data.query,
      total: searchResponse.data.total
    });

    console.log('\n🎉 所有API端点测试通过！');

  } catch (error) {
    console.error('❌ API测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testAPI();
