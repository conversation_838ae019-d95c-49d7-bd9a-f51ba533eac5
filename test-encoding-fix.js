// 测试编码修复效果
// 设置控制台编码为UTF-8以正确显示中文
if (process.platform === 'win32') {
  try {
    // 设置Node.js输出编码
    if (process.stdout.isTTY) {
      process.stdout.setEncoding('utf8');
    }
    if (process.stderr.isTTY) {
      process.stderr.setEncoding('utf8');
    }
  } catch (error) {
    // 忽略编码设置错误
  }
}

console.log('🧪 测试编码修复效果...');
console.log('=' .repeat(50));

// 模拟应用中的各种日志输出
console.log('✅ 正常启动: 畅游网络智能客服系统启动成功');
console.error('❌ 错误测试: 这是一个错误消息测试');
console.warn('⚠️ 警告测试: 这是一个警告消息测试');

console.log('\n📋 模拟实际应用日志:');
console.log('🔑 获取用户令牌成功');
console.error('❌ 用户验证失败: token_expired');
console.log('✅ OAuth令牌信息保存成功');
console.error('❌ 保存工作区鉴权信息失败: 数据库连接错误');

console.log('\n🔍 特殊字符测试:');
console.log('   Emoji: 🚀🔧📱💬🧪');
console.log('   中文标点: 《》、？！');
console.log('   混合内容: Token长度: 178字符');

console.log('\n🏁 编码测试完成');

// 测试错误对象
try {
  throw new Error('这是一个包含中文的错误消息');
} catch (error) {
  console.error('捕获到错误:', error.message);
}
