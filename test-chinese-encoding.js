// 测试中文编码显示
console.log('🧪 测试中文编码显示...');
console.log('=' .repeat(50));

console.log('✅ 正常日志: 这是一条正常的中文日志');
console.error('❌ 错误日志: 这是一条错误的中文日志');
console.warn('⚠️ 警告日志: 这是一条警告的中文日志');

console.log('\n📋 测试各种中文字符:');
console.log('   简体中文: 你好世界');
console.log('   繁体中文: 你好世界');
console.log('   特殊符号: ✅❌⚠️🔧🧪📋💡');
console.log('   数字混合: 第1个测试，第2个验证');

console.log('\n🔍 系统编码信息:');
console.log('   process.stdout.isTTY:', process.stdout.isTTY);
console.log('   process.platform:', process.platform);
console.log('   process.env.LANG:', process.env.LANG || '未设置');

// 测试不同的输出方式
console.log('\n📤 测试不同输出方式:');
process.stdout.write('stdout.write: 这是通过stdout.write输出的中文\n');
process.stderr.write('stderr.write: 这是通过stderr.write输出的中文\n');

console.log('\n🏁 测试完成');
