// Webhook测试脚本 - 模拟WhatsApp消息
const axios = require('axios');

const WEBHOOK_URL = 'http://localhost:3002/whatsapp-webhook';

async function testWebhook() {
  console.log('🧪 开始测试Webhook...\n');

  // 模拟WhatsApp消息数据
  const testMessage = {
    MessageSid: 'SM' + Math.random().toString(36).substr(2, 9),
    From: 'whatsapp:+1234567890',
    Body: '你好，这是一条测试消息',
    ProfileName: '测试用户'
  };

  try {
    console.log('📤 发送测试消息到WhatsApp webhook...');
    console.log('消息内容:', testMessage);

    const response = await axios.post(WEBHOOK_URL, testMessage, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      timeout: 10000
    });

    console.log('✅ Webhook响应状态:', response.status);
    console.log('✅ Webhook响应内容:', response.data || 'TwiML响应');

    console.log('\n🎉 Webhook测试完成！');
    console.log('💡 检查数据库是否记录了这条消息');

  } catch (error) {
    console.error('❌ Webhook测试失败:', error.response?.data || error.message);
  }
}

// 运行测试
testWebhook();
